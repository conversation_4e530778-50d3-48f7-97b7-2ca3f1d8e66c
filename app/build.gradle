plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
}

android {
    namespace 'com.example.wearcomm'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.wearcomm"
        minSdk 30
        targetSdk 34
        versionCode 1
        versionName "1.0"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.1'
    }
    externalNativeBuild {
        ndkBuild {
            path file('Android.mk')
        }
    }

    ndkVersion '25.2.9519653'
    packagingOptions{
        exclude 'META-INF/LICENSE.md'
        exclude 'META-INF/LICENSE-notice.md'
    }
}

dependencies {

    implementation libs.play.services.wearable
    implementation libs.androidx.preference
    implementation platform(libs.compose.bom)
    implementation libs.ui
    implementation libs.ui.tooling.preview
    implementation libs.compose.material
    implementation libs.compose.foundation
    implementation libs.activity.compose
    implementation libs.core.splashscreen
    androidTestImplementation platform(libs.compose.bom)
    androidTestImplementation libs.ui.test.junit4
    debugImplementation libs.ui.tooling
    debugImplementation libs.ui.test.manifest

    // 添加 Wear OS 相关依赖
    implementation libs.androidx.wear // 提供Wear UI库
    // implementation libs.androidx.wear.widget // 提供Wear UI库
    implementation libs.androidx.wear.input // 提供Wear 特有的输入方式
}